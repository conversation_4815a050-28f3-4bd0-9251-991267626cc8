version: '3.8'

services:
  # Your Backend (<PERSON><PERSON>'s part)
  backend:
    build:
      context: ./webApp
      dockerfile: Dockerfile.backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgres://avnadmin:<EMAIL>:12586/defaultdb?sslmode=require
      - REDIS_URL=redis://default:<EMAIL>:13637
      - PORT=3001
      - CORS_ORIGIN=http://localhost:5173
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Your Frontend (<PERSON><PERSON>'s part)
  frontend:
    build:
      context: ./webApp/client
      dockerfile: Dockerfile.frontend
    ports:
      - "5173:5173"
    environment:
      - VITE_API_URL=http://localhost:3001/api
      - VITE_SOCKET_URL=http://localhost:3001
    depends_on:
      - backend
    restart: unless-stopped

  # AI Engine (Subhasis's part)
  ai-engine:
    build:
      context: ./aiengine/kroolohack
      dockerfile: Dockerfile
    environment:
      - WEBSOCKET_URL=ws://backend:3001
      - API_BASE_URL=http://backend:3001/api
      - CAMERA_INDEX=0
      - LOG_LEVEL=INFO
      - ENABLE_GPU=false
      - SESSION_ID=demo-session-001
    volumes:
      - /dev/video0:/dev/video0  # Camera access
      - ./aiengine/kroolohack/logs:/app/logs
    devices:
      - /dev/video0:/dev/video0  # Camera device
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    privileged: true  # For camera access

networks:
  default:
    driver: bridge
