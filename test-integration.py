#!/usr/bin/env python3
"""
Integration Test Script for AI Engine + Backend
Tests the connection between Subhasis's AI engine and Sachin's backend
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BACKEND_URL = "http://localhost:3001"
API_BASE = f"{BACKEND_URL}/api"

def test_backend_health():
    """Test if backend is running"""
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is healthy")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to backend: {e}")
        return False

def test_session_creation():
    """Test session creation"""
    try:
        session_data = {
            "name": "AI Integration Test Session",
            "description": "Testing AI engine integration",
            "totalStudents": 25
        }
        
        response = requests.post(f"{API_BASE}/sessions", json=session_data, timeout=10)
        if response.status_code == 201:
            session_id = response.json()["data"]["session"]["id"]
            print(f"✅ Session created: {session_id}")
            return session_id
        else:
            print(f"❌ Session creation failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Session creation error: {e}")
        return None

def test_ai_metrics_endpoint(session_id):
    """Test AI metrics endpoint with sample data"""
    try:
        # Sample data in the format your AI engine will send
        metrics_data = {
            "sessionId": session_id,
            "metrics": {
                "attendance": {
                    "total_detected": 23,
                    "confidence_avg": 0.89
                },
                "engagement": {
                    "overall_score": 0.75,
                    "attention_score": 0.82,
                    "participation_score": 0.68,
                    "zones": {
                        "front": 0.85,
                        "middle": 0.72,
                        "back": 0.58
                    }
                },
                "audio": {
                    "noise_level": 0.35,
                    "speaker_activity": 0.78
                }
            },
            "timestamp": datetime.now().isoformat()
        }
        
        response = requests.post(f"{API_BASE}/ai/metrics", json=metrics_data, timeout=10)
        if response.status_code == 200:
            print("✅ AI metrics endpoint working")
            return True
        else:
            print(f"❌ AI metrics failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ AI metrics error: {e}")
        return False

def test_low_engagement_alert(session_id):
    """Test alert system with low engagement"""
    try:
        # Send low engagement data to trigger alerts
        low_engagement_data = {
            "sessionId": session_id,
            "metrics": {
                "attendance": {
                    "total_detected": 20,
                    "confidence_avg": 0.85
                },
                "engagement": {
                    "overall_score": 0.45,  # Below threshold
                    "attention_score": 0.50,
                    "participation_score": 0.40,
                    "zones": {
                        "front": 0.60,
                        "middle": 0.45,
                        "back": 0.30  # Very low
                    }
                },
                "audio": {
                    "noise_level": 0.80,  # High noise
                    "speaker_activity": 0.60
                }
            },
            "timestamp": datetime.now().isoformat()
        }
        
        response = requests.post(f"{API_BASE}/ai/metrics", json=low_engagement_data, timeout=10)
        if response.status_code == 200:
            print("✅ Alert system test completed")
            return True
        else:
            print(f"❌ Alert test failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Alert test error: {e}")
        return False

def main():
    """Run all integration tests"""
    print("🧪 AI Engine + Backend Integration Test")
    print("=" * 50)
    
    # Test 1: Backend Health
    if not test_backend_health():
        print("\n❌ Backend is not running. Please start it first:")
        print("   node src/server/simple-server.js")
        return False
    
    # Test 2: Session Creation
    session_id = test_session_creation()
    if not session_id:
        return False
    
    # Test 3: AI Metrics Endpoint
    if not test_ai_metrics_endpoint(session_id):
        return False
    
    # Test 4: Alert System
    if not test_low_engagement_alert(session_id):
        return False
    
    print("\n🎉 All Integration Tests Passed!")
    print(f"📊 Dashboard URL: http://localhost:5173")
    print(f"🔗 Session ID for AI Engine: {session_id}")
    print("\n✅ Your AI engine is ready to connect!")
    print("   Update SESSION_ID in .env file with the session ID above")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
