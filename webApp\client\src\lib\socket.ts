import { io, Socket } from 'socket.io-client';
// Temporary inline types to fix import issue
interface SocketEvents {
  // We'll define this as needed
}

class SocketClient {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  connect(): Socket {
    if (this.socket?.connected) {
      return this.socket;
    }

    const serverUrl = import.meta.env.VITE_SOCKET_URL || 'http://localhost:3001';
    
    this.socket = io(serverUrl, {
      transports: ['websocket', 'polling'],
      timeout: 10000,
      reconnection: true,
      reconnectionAttempts: this.maxReconnectAttempts,
      reconnectionDelay: this.reconnectDelay,
      reconnectionDelayMax: 5000,
      maxReconnectionAttempts: this.maxReconnectAttempts,
    });

    this.setupEventHandlers();
    return this.socket;
  }

  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('✅ Socket connected:', this.socket?.id);
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', (reason) => {
      console.log('❌ Socket disconnected:', reason);
      
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, reconnect manually
        this.socket?.connect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('🔌 Socket connection error:', error);
      this.reconnectAttempts++;
      
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.error('❌ Max reconnection attempts reached');
        this.socket?.disconnect();
      }
    });

    this.socket.on('reconnect', (attemptNumber) => {
      console.log(`🔄 Socket reconnected after ${attemptNumber} attempts`);
      this.reconnectAttempts = 0;
    });

    this.socket.on('reconnect_error', (error) => {
      console.error('🔄 Socket reconnection error:', error);
    });

    this.socket.on('reconnect_failed', () => {
      console.error('❌ Socket reconnection failed');
    });
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  getSocket(): Socket | null {
    return this.socket;
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // Session management
  joinSession(sessionId: string, userId?: string, role?: string): void {
    if (!this.socket?.connected) {
      console.warn('Socket not connected, cannot join session');
      return;
    }

    this.socket.emit('join-session', { sessionId, userId, role });
  }

  leaveSession(): void {
    if (!this.socket?.connected) {
      return;
    }

    this.socket.emit('leave-session');
  }

  // Alert management
  acknowledgeAlert(alertId: string): void {
    if (!this.socket?.connected) {
      console.warn('Socket not connected, cannot acknowledge alert');
      return;
    }

    this.socket.emit('acknowledge-alert', { alertId });
  }

  // Session control
  controlSession(sessionId: string, action: 'pause' | 'resume' | 'stop'): void {
    if (!this.socket?.connected) {
      console.warn('Socket not connected, cannot control session');
      return;
    }

    this.socket.emit('session-control', { sessionId, action });
  }

  // Event listeners
  onSessionJoined(callback: (data: any) => void): void {
    this.socket?.on('session-joined', callback);
  }

  onMetricsUpdated(callback: (data: any) => void): void {
    this.socket?.on('metrics-updated', callback);
  }

  onAlertTriggered(callback: (data: any) => void): void {
    this.socket?.on('alert-triggered', callback);
  }

  onAlertAcknowledged(callback: (data: any) => void): void {
    this.socket?.on('alert-acknowledged', callback);
  }

  onSessionStatusChanged(callback: (data: any) => void): void {
    this.socket?.on('session-status-changed', callback);
  }

  onUserJoined(callback: (data: any) => void): void {
    this.socket?.on('user-joined', callback);
  }

  onUserLeft(callback: (data: any) => void): void {
    this.socket?.on('user-left', callback);
  }

  onError(callback: (data: { message: string }) => void): void {
    this.socket?.on('error', callback);
  }

  // Remove event listeners
  offSessionJoined(callback?: (data: any) => void): void {
    this.socket?.off('session-joined', callback);
  }

  offMetricsUpdated(callback?: (data: any) => void): void {
    this.socket?.off('metrics-updated', callback);
  }

  offAlertTriggered(callback?: (data: any) => void): void {
    this.socket?.off('alert-triggered', callback);
  }

  offAlertAcknowledged(callback?: (data: any) => void): void {
    this.socket?.off('alert-acknowledged', callback);
  }

  offSessionStatusChanged(callback?: (data: any) => void): void {
    this.socket?.off('session-status-changed', callback);
  }

  offUserJoined(callback?: (data: any) => void): void {
    this.socket?.off('user-joined', callback);
  }

  offUserLeft(callback?: (data: any) => void): void {
    this.socket?.off('user-left', callback);
  }

  offError(callback?: (data: { message: string }) => void): void {
    this.socket?.off('error', callback);
  }

  // Remove all listeners
  removeAllListeners(): void {
    this.socket?.removeAllListeners();
  }
}

// Create singleton instance
export const socketClient = new SocketClient();

// Export for easier use
export default socketClient;
