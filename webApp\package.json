{"name": "classroom-engagement-analyzer", "version": "1.0.0", "description": "Real-time classroom engagement analyzer with AI-powered insights", "main": "dist/server.js", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "nodemon --exec ts-node src/server/index.ts", "dev:client": "cd client && npm run dev", "build": "npm run build:server && npm run build:client", "build:server": "tsc -p tsconfig.server.json", "build:client": "cd client && npm run build", "start": "node dist/server.js", "setup": "npm install && cd client && npm install", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:studio": "npx prisma studio"}, "keywords": ["hackathon", "education", "ai", "real-time", "engagement"], "author": "Sachin & Subhasis", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.24.1", "@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "prisma": "^5.7.1", "react-hot-toast": "^2.5.2", "redis": "^4.6.12", "socket.io": "^4.7.4", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "concurrently": "^8.2.2", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}