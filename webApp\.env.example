# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/classroom_analyzer?schema=public"

# Redis Configuration
REDIS_URL="redis://localhost:6379"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_EXPIRES_IN="24h"

# CORS Configuration
CORS_ORIGIN="http://localhost:5173"

# AI Pipeline Configuration
AI_PIPELINE_URL="http://localhost:8000"
AI_PIPELINE_WEBHOOK_SECRET="ai-webhook-secret"

# Logging
LOG_LEVEL="info"

# Real-time Configuration
SOCKET_IO_CORS_ORIGIN="http://localhost:5173"
REDIS_ADAPTER_KEY="classroom-analyzer"

# Alert Thresholds
ENGAGEMENT_ALERT_THRESHOLD=0.6
ATTENDANCE_ALERT_THRESHOLD=0.8
NOISE_ALERT_THRESHOLD=0.7
