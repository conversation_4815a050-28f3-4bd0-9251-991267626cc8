{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "outDir": "./dist", "rootDir": "./src", "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "skipLibCheck": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "client"]}