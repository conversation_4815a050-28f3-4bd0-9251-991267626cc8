@echo off
echo 🚀 Starting Full Classroom Engagement Analyzer System
echo ==================================================

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

echo 🔧 Building and starting all services...

REM Build and start all services
docker-compose -f docker-compose.full-system.yml up --build

echo 🎉 System started successfully!
echo.
echo 📊 Access Points:
echo    Frontend Dashboard: http://localhost:5173
echo    Backend API: http://localhost:3001
echo    Health Check: http://localhost:3001/health
echo.
echo 🤖 AI Engine Status:
echo    The AI engine will automatically connect to your backend
echo    and start processing camera feed for real-time analytics.
echo.
echo Press Ctrl+C to stop all services
pause
