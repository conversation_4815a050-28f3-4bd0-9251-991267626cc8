#!/bin/bash

echo "🚀 Starting Full Classroom Engagement Analyzer System"
echo "=================================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if camera is available
if [ ! -e /dev/video0 ]; then
    echo "⚠️  Warning: No camera detected at /dev/video0"
    echo "   The AI engine may not work properly without a camera."
    echo "   Make sure your webcam is connected."
fi

echo "🔧 Building and starting all services..."

# Build and start all services
docker-compose -f docker-compose.full-system.yml up --build

echo "🎉 System started successfully!"
echo ""
echo "📊 Access Points:"
echo "   Frontend Dashboard: http://localhost:5173"
echo "   Backend API: http://localhost:3001"
echo "   Health Check: http://localhost:3001/health"
echo ""
echo "🤖 AI Engine Status:"
echo "   The AI engine will automatically connect to your backend"
echo "   and start processing camera feed for real-time analytics."
echo ""
echo "Press Ctrl+C to stop all services"
