import axios from 'axios';
import type { AxiosInstance, AxiosResponse } from 'axios';
// Temporary inline types to fix import issue
interface Session {
  id: string;
  name: string;
  description?: string;
  startTime: string;
  endTime?: string;
  status: string;
  totalStudents?: number;
  createdAt: string;
  updatedAt: string;
}

interface Alert {
  id: string;
  sessionId: string;
  timestamp: string;
  type: string;
  severity: string;
  zone?: string;
  message: string;
  acknowledged: boolean;
}

interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add auth token if available
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse<any>>) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized access
          localStorage.removeItem('auth_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Session endpoints
  async getSessions(params?: {
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse<PaginatedResponse<Session>>> {
    const response = await this.client.get('/sessions', { params });
    return response.data;
  }

  async getSession(id: string): Promise<ApiResponse<{
    session: Session;
    realTime: {
      sessionState: any;
      latestMetrics: any;
      connectionCount: number;
    };
  }>> {
    const response = await this.client.get(`/sessions/${id}`);
    return response.data;
  }

  async createSession(data: {
    name: string;
    description?: string;
    totalStudents?: number;
    classroomLayout?: any;
  }): Promise<ApiResponse<{ session: Session }>> {
    const response = await this.client.post('/sessions', data);
    return response.data;
  }

  async updateSession(id: string, data: {
    name?: string;
    description?: string;
    status?: string;
    totalStudents?: number;
    classroomLayout?: any;
  }): Promise<ApiResponse<{ session: Session }>> {
    const response = await this.client.put(`/sessions/${id}`, data);
    return response.data;
  }

  async deleteSession(id: string): Promise<ApiResponse<void>> {
    const response = await this.client.delete(`/sessions/${id}`);
    return response.data;
  }

  async getSessionSummary(id: string): Promise<ApiResponse<{ summary: any }>> {
    const response = await this.client.get(`/sessions/${id}/summary`);
    return response.data;
  }

  // Metrics endpoints
  async getSessionMetrics(sessionId: string, params?: {
    limit?: number;
    offset?: number;
    startTime?: string;
    endTime?: string;
    interval?: string;
  }): Promise<ApiResponse<{
    metrics: SessionMetric[];
    aggregated: any;
    latest: any;
    pagination: {
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  }>> {
    const response = await this.client.get(`/metrics/session/${sessionId}`, { params });
    return response.data;
  }

  async getLatestMetrics(sessionId: string): Promise<ApiResponse<{
    metrics: any;
  }>> {
    const response = await this.client.get(`/metrics/session/${sessionId}/latest`);
    return response.data;
  }

  async getSessionStats(sessionId: string, params?: {
    timeRange?: string;
  }): Promise<ApiResponse<{
    stats: SessionStats;
  }>> {
    const response = await this.client.get(`/metrics/session/${sessionId}/stats`, { params });
    return response.data;
  }

  // Alerts endpoints
  async getSessionAlerts(sessionId: string, params?: {
    limit?: number;
    offset?: number;
    type?: string;
    severity?: string;
    acknowledged?: boolean;
    startTime?: string;
    endTime?: string;
  }): Promise<ApiResponse<{
    alerts: Alert[];
    activeAlerts: Alert[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  }>> {
    const response = await this.client.get(`/alerts/session/${sessionId}`, { params });
    return response.data;
  }

  async getAlert(id: string): Promise<ApiResponse<{ alert: Alert }>> {
    const response = await this.client.get(`/alerts/${id}`);
    return response.data;
  }

  async acknowledgeAlert(id: string): Promise<ApiResponse<{ alert: Alert }>> {
    const response = await this.client.put(`/alerts/${id}/acknowledge`);
    return response.data;
  }

  async acknowledgeAllAlerts(sessionId: string, filters?: {
    type?: string;
    severity?: string;
  }): Promise<ApiResponse<{ acknowledgedCount: number }>> {
    const response = await this.client.put(`/alerts/session/${sessionId}/acknowledge-all`, filters);
    return response.data;
  }

  async getAlertSummary(sessionId: string, params?: {
    timeRange?: string;
  }): Promise<ApiResponse<{
    summary: {
      total: number;
      acknowledged: number;
      unacknowledged: number;
      byType: Record<string, number>;
      bySeverity: Record<string, number>;
      byZone: Record<string, number>;
      timeline: any[];
      recentAlerts: Alert[];
      criticalAlerts: Alert[];
    };
  }>> {
    const response = await this.client.get(`/alerts/session/${sessionId}/summary`, { params });
    return response.data;
  }

  async deleteAlert(id: string): Promise<ApiResponse<void>> {
    const response = await this.client.delete(`/alerts/${id}`);
    return response.data;
  }

  // AI endpoints
  async getAIStatus(sessionId: string): Promise<ApiResponse<{
    status: {
      isActive: boolean;
      lastActivity: string | null;
      latestMetrics: any;
      healthCheck: {
        cacheConnected: boolean;
        databaseConnected: boolean;
        lastHeartbeat: string;
      };
    };
  }>> {
    const response = await this.client.get(`/ai/session/${sessionId}/status`);
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<{
    status: string;
    timestamp: string;
    uptime: number;
  }> {
    const response = await this.client.get('/health');
    return response.data;
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Export individual methods for easier use
export const {
  getSessions,
  getSession,
  createSession,
  updateSession,
  deleteSession,
  getSessionSummary,
  getSessionMetrics,
  getLatestMetrics,
  getSessionStats,
  getSessionAlerts,
  getAlert,
  acknowledgeAlert,
  acknowledgeAllAlerts,
  getAlertSummary,
  deleteAlert,
  getAIStatus,
  healthCheck,
} = apiClient;
